﻿
using FairyGUI;
using System;
using System.Collections.Generic;
using UnityEngine;

public class Panel
{
    protected string packName;
    protected string compName;
    protected bool isBlurLayer = false;
    protected bool modal = false;
    protected bool inQueue = false;
    protected bool isFullScreen = true;
    private bool cache = false;
    protected GComponent contentPane;
    public Action<int> OnClosed;
    protected List<MediatorBase> _mediatorList;
    protected bool disableAnimation; //屏蔽显示和隐藏动画

    protected Window _window;
    private void Init()
    {
        _window = new Window();
        contentPane = _window.contentPane = UIPackage.CreateObject(packName, compName).asCom;
        if (isFullScreen)
        {
            _window.MakeFullScreen();
        }

#if UNITY_STANDALONE
        //窗口改变自适应
        _window.AddRelation(GRoot.inst, RelationType.Size);
#endif
        // NetConnection.GetInstance().OnMessageReceived += OnMessageReceived;
        contentPane.onClick.Add(OnClick);
        _window.modal = modal;

        DoInitialize();
    }

    public static float ButtleCdTime = 0.3f;
    private Dictionary<string, float> buttonCdDic = new Dictionary<string, float>();
    private void OnClick(EventContext context)
    {
        var target = context.initiator as DisplayObject;
        if (target == null || target.gOwner == null)
            return;

        var targetName = target.gOwner.name;
        buttonCdDic.TryGetValue(targetName, out float preClickTime);
        if (Time.realtimeSinceStartup - preClickTime < ButtleCdTime)
        {
            return;
        }
        buttonCdDic[targetName] = Time.realtimeSinceStartup;
        this.OnMouseClick(targetName);
    }

    protected void matchScreen(GObject img)
    {
        float s1 = GRoot.inst.width / img.width;
        float s2 = GRoot.inst.height / img.height;
        float s = s1 > s2 ? s1 : s2;
        img.scale = Vector2.one * s;
        img.x = -(img.width * s - GRoot.inst.width) / 2f;
        img.y = -(img.height * s - GRoot.inst.height) / 2f;
    }

    protected virtual void DoInitialize()
    {

    }
    // protected virtual void OnMessageReceived(NetMessage msg)
    // {

    // }
    protected virtual void OnMouseClick(string targetName)
    {

    }
    public string GetCurPackRes(string name)
    {
        return "ui://" + this.packName + "/" + name;
    }
    protected bool IsShowing
    {
        get
        {
            return _window.isShowing;
        }
    }

    public void Show()
    {
        _window.Show();
        if (disableAnimation || !modal)
        {
            OnShow();
        }
        else
        {
            DoShowAnimation();
        }
    }

    protected virtual void OnShow()
    {

    }
    public void Hide(int closeType = 0)
    {
        // NetConnection.GetInstance().OnMessageReceived -= OnMessageReceived;
        NotifyMgr.OffAllCaller(this);
        OnClosed?.Invoke(closeType);
        OnHide();
        _window.Hide();

        if (!cache)
        {
            this._ClearAllMediator();
            _window.Dispose();
        }
    }

    protected MediatorBase AddMediator(MediatorBase mediator)
    {
        if (_mediatorList == null)
        {
            _mediatorList = new List<MediatorBase>();
        }
        _mediatorList.Add(mediator);
        return mediator;
    }

    protected void RemoveMediator(MediatorBase mediator)
    {
        if (_mediatorList != null)
        {
            this._mediatorList.Remove(mediator);
        }
    }

    private void _ClearAllMediator()
    {
        if (_mediatorList != null)
        {
            _mediatorList.ForEach((MediatorBase mediator) =>
            {
                mediator.Remove();
            });
            _mediatorList = null;
        }
    }

    protected virtual void OnHide()
    {

    }


    //处理显示动画
    protected virtual void DoShowAnimation()
    {
        contentPane.SetScale(0f, 0f);
        contentPane.TweenScale(new Vector2(1.0f, 1.0f), 0.3f).SetEase(EaseType.BackOut).SetIgnoreEngineTimeScale(true);
        contentPane.alpha = 0.5f;
        contentPane.SetPivot(0.5f, 0.5f);
        contentPane.TweenFade(1f, 0.3f).SetIgnoreEngineTimeScale(true).OnComplete(() =>
        {
            OnShow();
        });
    }

    //处理隐藏动画
    protected void DoHideAnimation()
    {
        if (disableAnimation)
        {
            _window.HideImmediately();
            return;
        }
        _window.HideImmediately();
    }

    private void _Create<T>(Action<T> callBack = null) where T : Panel, new()
    {
        if (_window == null)
        {
            FUILoader.LoadPackage(packName, () =>
            {
                this.Init();
                this.Show();

                callBack?.Invoke((T)this);
            });
        }
        else
        {
            Show();
            callBack?.Invoke((T)this);
        }
    }


    //todo 缓存清理时机
    private static Dictionary<string, Panel> cacheDic = new Dictionary<string, Panel>();
    /// <summary>
    /// 创建界面
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="callBack"></param>
    /// <param name="cache">如果需要缓存界面，逻辑上需要支持重复使用
    /// DoInitialize只有创建的时候调用一次，需要重复调用的逻辑需要覆写OnShow</param>
    public static void Create<T>(Action<T> callBack = null, bool cache = false) where T : Panel, new()
    {
        Type panelType = typeof(T);
        Panel panel = null;
        if (cache)
        {
            cacheDic.TryGetValue(panelType.ToString(), out panel);
        }

        if (panel == null)
        {
            panel = new T();

            if (cache)
            {
                cacheDic.Add(panelType.ToString(), panel);
            }
        }

        panel.cache = cache;
        panel._Create<T>(callBack);
    }

    public static void Create(string panelClassName, Action<Panel> callBack = null, CallBackUtil.SimpleCallBack closeCallback = null, bool autoShow = true, bool closedRemoveRes = true)
    {
        Type t = Type.GetType(panelClassName);
        if (t == null)
        {
            UnityEngine.Debug.LogWarning("[Panel] Can't find class :" + panelClassName);
            return;
        }

        Panel panel = t.Assembly.CreateInstance(panelClassName) as Panel;
        panel._Create<Panel>(callBack);

    }
}

