using System;
using DG.Tweening;
using FairyGUI;
using UnityEngine;

public class BoxLevelBar
{
    public Action OnGetReward;
    private GComponent contentPane;
    private GProgressBar progressStar;
    private GList listReward;
    private ItemVo[] rewards;
    public BoxLevelBar(GObject gObj, GComponent parent)
    {
        contentPane = gObj.asCom;
        progressStar = contentPane.GetChild("progressStar").asProgress;
        var boxReward = contentPane.GetChild("boxReward").asCom;
        listReward = boxReward.GetChild("listReward").asList;
        listReward.itemRenderer = UpdateRewardItem;

        UpdateProgress();
        contentPane.onClick.Set(() =>
        {
            if (progressStar.value >= progressStar.max)
            {
                for (int i = 0; i < rewards.Length; i++)
                {
                    var itemVo = rewards[i];
                    var pos = contentPane.xy;
                    pos.x += -100 + i * 100;
                    UITweenUtil.FlyItem(parent, pos, itemVo.itemId, itemVo.count);
                    GameGlobal.IncrementItemCount(itemVo.itemId, itemVo.count);
                }

                GameGlobal.ResetPassLevel();
                GameGlobal.IncrementPassLevelIndex();
                if (GameGlobal.PassLevelIndex >= ConfigBoxLevel.MaxRewardLevel)
                {
                    GameGlobal.ResetPassLevelIndex();
                }
                UpdateProgress();

                SystemFacade.RedPointSystem.TriggerRefresh();
                NotifyMgr.Event(NotifyNames.UpdateItemCount);
            }
            else
            {
                if (!boxReward.visible)
                {
                    DOVirtual.DelayedCall(3, () =>
                    {
                        if (!boxReward.isDisposed)
                        {
                            boxReward.visible = false;
                        }
                    });
                    boxReward.visible = true;
                }
            }
        });
    }

    private void UpdateProgress()
    {
        var infoBoxLevel = ConfigBoxLevel.GetReward(GameGlobal.PassLevelIndex);
        var curPassLevel = Mathf.Min(GameGlobal.PassLevel, infoBoxLevel.needPassLevel);
        SetValue(curPassLevel, infoBoxLevel.needPassLevel);
        rewards = infoBoxLevel.Rewards;

        listReward.numItems = rewards.Length;
        listReward.ResizeToFit();
    }

    public void UpdateProgressText()
    {
        var infoBoxLevel = ConfigBoxLevel.GetReward(GameGlobal.PassLevelIndex);
        var curPassLevel = Mathf.Min(GameGlobal.PassLevel, infoBoxLevel.needPassLevel);
        var canGetReward = curPassLevel >= infoBoxLevel.needPassLevel;
        contentPane.asButton.text = canGetReward ? LangUtil.GetText("txtGateBoxDesc2") : LangUtil.GetText("txtGateBoxDesc1", infoBoxLevel.needPassLevel - curPassLevel);
    }

    private void UpdateRewardItem(int index, GObject item)
    {
        var icon = item.asCom.GetChild("icon").asLoader;
        var txtCount = item.asCom.GetChild("txtCount").asTextField;
        if (index >= rewards.Length)
            return;
        var itemVo = rewards[index];
        var infoItem = ConfigItem.GetData(itemVo.itemId);
        icon.url = infoItem.IconUrl;
        txtCount.text = itemVo.count.ToString();
    }

    public void TweenValue(int value)
    {
        progressStar.TweenValue(value, 0.5f);
    }
    public void SetValue(int value, int max)
    {
        progressStar.max = max;
        progressStar.value = value;
        UpdateProgressText();
    }

    public Vector2 GetPos()
    {
        return contentPane.position;
    }
}