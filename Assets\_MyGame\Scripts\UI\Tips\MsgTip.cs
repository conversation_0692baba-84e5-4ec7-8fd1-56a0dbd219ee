using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using FairyGUI;

public class MsgTip : Panel
{
    public MsgTip()
    {
        packName = "Tips";
        compName = "TipPanel";
        modal = false;
    }

    private GTextField contentTxt;


    protected override void DoInitialize()
    {
        contentTxt = contentPane.GetChild("lblText").asTextField;
    }

    private void TimerCallback(object data)
    {
        if (!IsShowing)
            return;

        this.Hide();
    }

    protected override void DoShowAnimation()
    {
        contentPane.alpha = 0.5f;
        contentPane.TweenFade(1f, 0.2f);
    }

    public static void Create(string content, float duration = 12f)
    {
        Create((MsgTip tip) =>
        {
            // tip.contentTxt.UBBEnabled = true;
            // tip.contentTxt.text = HtmlUBBUtil.HtmlToUBB(content);
            tip.contentTxt.text = content;
            tip.Show();

            Timers.inst.Add(duration, 1, tip.TimerCallback);
        });
    }
}
