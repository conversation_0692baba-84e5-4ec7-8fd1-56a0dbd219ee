<?xml version="1.0" encoding="utf-8"?>
<packageDescription id="2860ov62">
  <resources>
    <component id="oowk0" name="GetRewardPanel.xml" path="/" exported="true"/>
    <component id="oowk1" name="ListItem.xml" path="/"/>
    <image id="r3ml2" name="123.png" path="/images/"/>
    <misc id="inpg7" name="GongXiHuoDe_tw.atlas" path="/spine/"/>
    <image id="inpg8" name="GongXiHuoDe_tw.png" path="/spine/"/>
    <spine id="inpg9" name="GongXiHuoDe_tw.skel" path="/spine/" width="542" height="162" require="inpg7,inpg8" atlasNames="GongXiHuoDe_tw" anchor="244,96" shader="FairyGUI/Image"/>
    <image id="nem3a" name="0_00000.png" path="/effect/ItemLight/"/>
    <image id="nem3b" name="0_00002.png" path="/effect/ItemLight/"/>
    <image id="nem3c" name="0_00004.png" path="/effect/ItemLight/"/>
    <image id="nem3d" name="0_00006.png" path="/effect/ItemLight/"/>
    <image id="nem3e" name="0_00008.png" path="/effect/ItemLight/"/>
    <image id="nem3f" name="0_00010.png" path="/effect/ItemLight/"/>
    <image id="nem3g" name="0_00012.png" path="/effect/ItemLight/"/>
    <image id="nem3h" name="0_00014.png" path="/effect/ItemLight/"/>
    <image id="nem3i" name="0_00016.png" path="/effect/ItemLight/"/>
    <image id="nem3j" name="0_00018.png" path="/effect/ItemLight/"/>
    <image id="nem3k" name="0_00020.png" path="/effect/ItemLight/"/>
    <image id="nem3l" name="0_00022.png" path="/effect/ItemLight/"/>
    <image id="nem3m" name="0_00024.png" path="/effect/ItemLight/"/>
    <image id="nem3n" name="0_00026.png" path="/effect/ItemLight/"/>
    <image id="nem3o" name="0_00028.png" path="/effect/ItemLight/"/>
    <image id="nem3p" name="0_00030.png" path="/effect/ItemLight/"/>
    <movieclip id="nem3q" name="ItemLight.jta" path="/" atlas="alone"/>
  </resources>
  <publish name=""/>
</packageDescription>