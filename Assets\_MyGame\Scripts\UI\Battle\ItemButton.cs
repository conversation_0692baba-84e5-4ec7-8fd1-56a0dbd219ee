using System;
using FairyGUI;
using UnityEngine;

public class ItemButton
{
    public GComponent contentPane;
    private GTextField txtCount;
    private GImage imgAdd;
    private GGroup boxCount;
    private GObject boxItem;
    private GLoader icon;
    private bool isLock;
    private int _itemId;
    private int _canUseMax;
    private Transition niuAni;
    public ItemButton(int itemId, int canUsedMax, GObject gObj)
    {
        contentPane = gObj.asCom;
        _itemId = itemId;
        _canUseMax = canUsedMax;
        icon = contentPane.GetChild("icon").asLoader;
        boxCount = contentPane.GetChild("boxCount").asGroup;
        txtCount = contentPane.GetChild("txtCount").asTextField;
        imgAdd = contentPane.GetChild("imgAdd").asImage;
        boxItem = contentPane.GetChild("boxItem");
        niuAni = contentPane.GetTransition("niu");

        // RandomPlayNiuAni();
    }


    public void SetItemCount(int count)
    {
        txtCount.text = count.ToString();
        imgAdd.visible = count == 0;
        boxCount.visible = !imgAdd.visible;
    }

    public bool CanUse
    {
        get
        {
            return true;
        }
    }

    public Vector2 GetPos()
    {
        return contentPane.xy;
    }

    private void RandomPlayNiuAni()
    {
        if (!CanUse)
            return;
        Timers.inst.Add(UnityEngine.Random.Range(5, 15), 1, PlayNiuAni);
    }

    private void PlayNiuAni(object obj)
    {
        if (contentPane.isDisposed)
            return;

        niuAni.Play();
        RandomPlayNiuAni();
    }
}