<?xml version="1.0" encoding="utf-8"?>
<component size="310,115" pivot="0.5,0.5" anchor="true" extention="Button">
  <displayList>
    <movieclip id="n20_gu4g" name="n20" src="eji24h" fileName="effect/boxBar/boxBar.jta" xy="-4,-4"/>
    <richtext id="n19_ufgj" name="title" xy="17,74" size="278,34" fontSize="24" color="#ffffff" align="center" vAlign="middle" ubb="true" autoSize="none" bold="true" singleLine="true" autoClearText="true" text="还有[color=#fffc00]1关[/color]领奖励"/>
    <component id="n16_m6gv" name="progressStar" src="eji24i" fileName="ProgressBar1.xml" xy="123,32" touchable="false">
      <ProgressBar value="50" max="100"/>
    </component>
    <component id="n18_t5i1" name="boxReward" src="eji24l" fileName="BoxRewardTip.xml" xy="12,108" visible="false" touchable="false"/>
    <image id="n21_v6gw" name="imgRedDot" src="hl2b4f" fileName="images/image_redDot2.png" xy="275,0" pivot="0.5,0.5" visible="false">
      <relation target="" sidePair="right-right,top-top"/>
    </image>
  </displayList>
  <Button downEffect="scale" downEffectValue="0.9"/>
  <transition name="t0" autoPlay="true" autoPlayRepeat="-1">
    <item time="0" type="Rotation" target="n21_v6gw" tween="true" startValue="0" endValue="-20" duration="2" ease="Linear"/>
    <item time="2" type="Rotation" target="n21_v6gw" tween="true" startValue="-20" endValue="20" duration="2" ease="Linear"/>
    <item time="4" type="Rotation" target="n21_v6gw" tween="true" startValue="20" endValue="-20" duration="2" ease="Linear"/>
    <item time="6" type="Rotation" target="n21_v6gw" tween="true" startValue="-20" endValue="20" duration="2" ease="Linear"/>
    <item time="8" type="Rotation" target="n21_v6gw" tween="true" startValue="20" endValue="0" duration="1" ease="Linear"/>
    <item time="32" type="Rotation" target="n21_v6gw" value="0"/>
  </transition>
</component>