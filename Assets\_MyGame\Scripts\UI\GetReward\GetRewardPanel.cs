using System;
using FairyGUI;
using DG.Tweening;
using UnityEngine;

public class GetRewardPanel : Panel
{
    public GetRewardPanel()
    {
        packName = "GetReward";
        compName = "GetRewardPanel";
    }

    private GList listReward;
    private float itemShowDelay = 0.1f; // 每个item显示的延迟时间
    private Vector2? flyTarget;
    private float flyDuration = 0.5f;
    private float flyItemDelay = 0.05f;
    private GObject imgBg;
    private Controller c1;
    private bool canClick = false; // 添加标志控制是否可以点击

    protected override void DoInitialize()
    {
        c1 = contentPane.GetController("c1");
        imgBg = contentPane.GetChild("imgBg");
        imgBg.alpha = 0;
        imgBg.TweenFade(1, 0.2f);
        imgBg.onClick.Set(OnClickBg);
        listReward = contentPane.GetChild("listReward").asList;
        listReward.itemRenderer = UpdateItem;

        var effect = contentPane.GetChild("effect").asLoader3D;
        effect.spineAnimation.Play("show", (track) =>
        {
            if (contentPane.isDisposed)
                return;
            effect.spineAnimation.Play("idle", true);
        });

        SoundManager.PlayEffect("getReward");
    }

    public void SetData(ItemVo[] items, Vector2? flyTarget = null, float autoHide = 0)
    {
        canClick = false; // 重置点击状态
        this.flyTarget = flyTarget;
        listReward.data = items;
        listReward.numItems = items.Length;

        // 重置所有item的初始状态并播放动画
        for (int i = 0; i < listReward.numItems; i++)
        {
            var item = listReward.GetChildAt(i);
            item.alpha = 0;
            item.SetScale(0.5f, 0.5f);

            item.TweenScale(Vector2.one, 0.3f)
                .SetDelay(i * itemShowDelay)
                .SetEase(EaseType.BackOut);

            // 对最后一个item的fade动画添加完成回调
            if (i == listReward.numItems - 1)
            {
                item.TweenFade(1, 0.2f)
                    .SetDelay(i * itemShowDelay)
                    .OnComplete((obj) =>
                    {
                        canClick = true;

                        if (autoHide > 0)
                        {
                            Timers.inst.Add(autoHide, 1, OnTimeOut);
                        }
                    });
            }
            else
            {
                item.TweenFade(1, 0.2f)
                    .SetDelay(i * itemShowDelay);
            }
        }
    }
    private void OnTimeOut(object obj)
    {
        OnClickBg();
    }

    private void UpdateItem(int index, GObject item)
    {
        var icon = item.asCom.GetChild("icon").asLoader;
        var txtCount = item.asCom.GetChild("txtCount").asTextField;

        var data = (item.parent.data as ItemVo[])[index];
        var infoItem = ConfigItem.GetData(data.itemId);
        icon.url = infoItem.IconUrl;
        txtCount.text = "x" + data.count.ToString();
    }

    private void OnClickBg()
    {
        if (!canClick) return; // 如果动画未完成，不响应点击
        canClick = false;
        Timers.inst.Remove(OnTimeOut);

        if (flyTarget.HasValue)
        {
            PlayFlyAnimation();
        }
        else
        {
            Hide();
        }
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnConfirm":
                OnClickBg();
                break;
        }
    }

    protected override void OnHide()
    {
        Timers.inst.Remove(OnTimeOut);
    }

    private void PlayFlyAnimation()
    {
        c1.selectedIndex = 1;//开始飞行
        imgBg.TweenFade(0, 0.2f);

        var count = listReward.numItems;
        for (int i = 0; i < count; i++)
        {
            var item = listReward.GetChildAt(0);
            var globalPos = item.LocalToRoot(Vector2.zero, GRoot.inst);
            listReward.RemoveChild(item);

            GRoot.inst.AddChild(item);
            item.pivotAsAnchor = true;//同时设为锚点，xy需要做下偏移
            item.SetXY(globalPos.x + item.width * 0.5f, globalPos.y + item.height * 0.5f);

            // 先缩小并淡出
            item.TweenScale(new Vector2(0.5f, 0.5f), flyDuration)
                .SetDelay(i * flyItemDelay)
                .SetEase(EaseType.QuadIn);

            var curIndex = i;
            item.TweenMove(flyTarget.Value, flyDuration)
                .SetDelay(i * flyItemDelay)
                .SetEase(EaseType.BackIn).OnComplete((obj) =>
                {
                    item.Dispose(); // 确保清理移除的item

                    // 最后一个道具动画结束后关闭面板
                    if (curIndex == count - 1)
                    {
                        Hide();
                    }
                });
        }
    }
}