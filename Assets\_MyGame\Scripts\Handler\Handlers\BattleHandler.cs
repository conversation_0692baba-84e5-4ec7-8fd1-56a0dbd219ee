﻿
using DG.Tweening;
using FairyGUI;
using System.Collections.Generic;
using System.Text;
using UnityEngine;

public class BattleHandler : HandlerBase
{
    private static string BattleSceneUrl = "Scenes/BattleScene";

    private BattlePanel battlePanel;
    private BattleScene battleScene;
    private GameObject scenePrefab;

    private float startTime;
    public override void OnEnter()
    {
        startTime = Time.realtimeSinceStartup;
        SoundManager.PlayBg("bgm");

        if (GameGlobal.Level > 2)
        {
            LoadingPanel.Show();
        }

        isUILoaded = isSceneLoaded = false;
        Platform.Instance.ReportScene(1006);//加载游戏场景
        AssetBundleManager.LoadPrefab(BattleSceneUrl, OnScenePrefabLoaded);
        FUILoader.LoadPackage("Battle", OnUILoaded);
        // AssetBundleManager.LoadScene(BattleSceneUrl, LoadSceneMode.Additive, OnLoadSceneComplete);

        // #if UNITY_EDITOR
        //         MouseHand.Create();
        // #endif
    }

    private bool isUILoaded;
    private bool isSceneLoaded;
    private void OnUILoaded()
    {
        isUILoaded = true;
        OnSceneReady();
    }

    private void OnScenePrefabLoaded(GameObject scenePrefab)
    {
        isSceneLoaded = true;

        this.scenePrefab = scenePrefab;
        battleScene = GameObject.Instantiate(scenePrefab).GetComponent<BattleScene>();

        OnSceneReady();
    }

    private void OnSceneReady()
    {
        if (!isUILoaded || !isSceneLoaded)
            return;

        OnConfigComplete();
    }

    async private void OnConfigComplete()
    {
        Platform.Instance.ReportScene(1007);//加载池资源
        await PoolMgr.Inst.PrepareRes();

        Platform.Instance.ReportScene(1008);//加载battle界面
        Panel.Create((BattlePanel panel) =>
        {
            LoadingPanel.HideWhenFullProgress();
            battlePanel = panel;
            StartGame();
        });
    }

    public void StartGame()
    {
        var level = GameGlobal.EnterLevel;
        FlSdk.Inst.ReportChapterPlay();
        GameGlobal.IncrementLevelPlayCount(level);

        battleScene.slotBar.OnGameDone = OnGameDone;
        battlePanel.GameStart(this);

        if (level == 1)
        {

            battleScene.StartGuide(3, new int[] { 9, 8, 7, 8, 9, 7, 7, 9, 8 });
            UpdateCameraY(0);
            battlePanel.SetLeftTime(60 * 10);
        }
        else
        {
            var gameSetting = ConfigSetting.Setting;
            var infoGate = ConfigGate.GetData(level);
            var typeCount = infoGate.majiangTypeCount;
            var totalCount = infoGate.majiangCount;
            int topCount = infoGate.topCount;
            int topTypeCount = infoGate.topTypeCount;

            battlePanel.SetLeftTime(infoGate.gameTime);
#if UNITY_EDITOR
            //test
            // typeCount = 5;
            // totalCount = 12;
            // topTypeCount = 0;
            // topCount = 0;
#endif
            var lerpValue = Mathf.InverseLerp(gameSetting.startMajiangCount, gameSetting.maxMajiangCount, totalCount);
            UpdateCameraY(lerpValue);
            if (totalCount % 3 != 0)
            {
                Debug.LogError("总数不是3的倍数");
                return;
            }

            // sb.Clear();
            // for (int i = 1; i <= 60; i++)
            // {
            //     infoGate = ConfigGate.GetData(i);
            //     CalTypeProbability(i, infoGate.majiangTypeCount, infoGate.majiangCount, infoGate.topTypeCount, infoGate.topCount, infoGate.uniqueTypeCount);
            // }
            // Log.Info(sb.ToString());

            var nums = CreateRandomNums(typeCount, totalCount, topTypeCount, topCount);
            Debug.Log("type:" + typeCount + ",createCount：" + totalCount);

            var lockSlotCount = infoGate.lockSlotCount;
            battleScene.StartGame(this, typeCount + topTypeCount, topCount, nums, lockSlotCount);
        }
    }

    private StringBuilder sb = new StringBuilder();
    private void CalTypeProbability(int gate, int typeCount, int totalCount, int topTypeCount, int topCount)
    {
        Dictionary<int, int> map = new Dictionary<int, int>();

        var result = new int[6];
        var total = new int[6];
        var count = 10000f;
        for (int i = 0; i < count; i++)
        {
            map.Clear();
            for (int l = 0; l < result.Length; l++)
            {
                result[l] = 0;
            }
            var nums = CreateRandomNums(typeCount, totalCount, topTypeCount, topCount);
            for (int j = 0; j < nums.Count; j++)
            {
                if (!map.ContainsKey(nums[j]))
                {
                    map[nums[j]] = 0;
                }
                map[nums[j]]++;
            }

            foreach (var item in map)
            {
                if (item.Value % 3 == 0)
                {
                    var idx = item.Value / 3 - 1;
                    if (idx < result.Length)
                    {
                        result[idx]++;
                    }
                }
            }
            for (int z = 0; z < result.Length; z++)
            {
                total[z] += result[z];
                // sb.Append($"{z + 1}->{result[z]} /{(float)totalTypeCount}, ");
            }
        }

        var totalTypeCount = Mathf.FloorToInt((totalCount - topCount) / 3);
        for (int i = 0; i < total.Length; i++)
        {
            sb.Append($"gate:{gate}\t{i + 1}->{total[i] / count} /{(float)totalTypeCount}, \t\t");
        }
        sb.AppendLine();
    }

    private List<int> CreateRandomNums(int typeCount, int totalCount, int topTypeCount, int topCount)
    {
        var nums = new List<int>();

        var allTypes = GetRandomTypes(typeCount + topTypeCount);
        //底层麻将
        var bottomTypes = allTypes.GetRange(0, typeCount);
        var bottomGroupCount = Mathf.FloorToInt(totalCount / 3f);
        for (int i = 0; i < bottomGroupCount; i++)
        {
            var num = bottomTypes[Random.Range(0, typeCount)];
            nums.Add(num);
            nums.Add(num);
            nums.Add(num);
        }

        //顶层麻将
        var topTypes = allTypes.GetRange(typeCount, topTypeCount);
        var topGroupCount = Mathf.FloorToInt(topCount / 3f);
        for (int i = 0; i < topGroupCount; i++)
        {
            var num = topTypes[Random.Range(0, topTypeCount)];
            // var mj = ConfigMajiang.GetData(num);
            // Log.Debug($"顶层麻将{i + 1}:{mj.name}");

            nums.Add(num);
            nums.Add(num);
            nums.Add(num);
        }
        return nums;
    }

    private List<int> GetRandomTypes(int typeCount)
    {
        var types = new List<int>();
        for (int i = 0; i < typeCount; i++)
        {
            int num = i + 1;
            types.Add(num);
        }
        //打乱types顺序
        for (int i = 0; i < typeCount; i++)
        {
            var randomIdx = Random.Range(0, typeCount);
            var tempValue = types[i];
            types[i] = types[randomIdx];
            types[randomIdx] = tempValue;
        }
        return types;
    }

    private void UpdateCameraY(float lerpValue)
    {
        // float cameraY = Mathf.Lerp(8.4f, 8.6f, lerpValue);
        float cameraY = Mathf.Lerp(7.6f, 9.2f, lerpValue);
        // cameraY = 8.4f;
        var cameraPos = battleScene.mainCamera.transform.position;
        cameraPos.y = cameraY;
        battleScene.mainCamera.transform.position = cameraPos;
        UnityEngine.Debug.Log("cameraPos:" + cameraPos);

        //-3.4  -4.18
        var slotBarPos = battleScene.slotBar.transform.position;
        battleScene.MoveBar(Mathf.Lerp(-3.55f, -4.4f, lerpValue));
    }

    public void Pause()
    {
        battlePanel?.Pause();
    }
    public void Resume()
    {
        battlePanel?.Resume();
    }

    public void OnGameDone(bool isWin)
    {
        Timers.inst.Add(0.5f, 1, (obj) =>
        {
            if (this == null)
                return;
            if (isWin)
            {
                ShowWin();
            }
            else
            {
                battlePanel?.GameOver();
            }
        });
    }

    private void ShowWin()
    {
        GameGlobal.ResetLevelRestartCount();

        if (GameGlobal.EnterLevel == 1)
        {
            GameGlobal.IncrementLevel();
            // GameGlobal.IncrementPassLevel();
            FlSdk.Inst.ReportChapterLevel();
            new CmdEnterBattle().Execute(2, true, true);
        }
        else
        {
            battlePanel?.GameWin();
            GameGlobal.IncrementLevel();
            GameGlobal.IncrementPassLevel();
            FlSdk.Inst.ReportChapterLevel();
        }
    }

    public override void OnExit()
    {
        MaJiang.NextId = 1;
        battlePanel?.Hide();
        GameObject.Destroy(battleScene.gameObject);
        battleScene = null;
    }
}