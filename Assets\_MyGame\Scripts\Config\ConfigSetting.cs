﻿using System.Collections.Generic;

public class ConfigSetting : ConfigBase
{
    private static SettingWrapper _setting;
    public static SettingWrapper Setting
    {
        get
        {
            _setting ??= new SettingWrapper();
            return _setting;
        }
    }
    public class SettingWrapper
    {
        /// <summary>每过几关提升难度</summary>
        public int everyLevelIncreaseDifficulty;
        /// <summary>第一关多少花色</summary>
        public int startHuaCount;
        /// <summary>每过n关加多少种花色</summary>
        public int addHuaCount;
        /// <summary>花色上限</summary>
        public int maxHuaCount;
        /// <summary>第一关多少麻将</summary>
        public int startMajiangCount;
        /// <summary>每过n关加多少麻将(需要3的倍数)</summary>
        public int addMajiangCount;
        /// <summary>麻将数量上限</summary>
        public int maxMajiangCount;
        /// <summary>体验道具数量</summary>
        public int guideItemCount;
        /// <summary>可复活次数</summary>
        public int reliveCount;
        /// <summary>清空槽位所需金币</summary>
        public int clearSlotBarNeedGold;
        /// <summary>加时所需金币</summary>
        public int addTimeNeedGold;
        /// <summary>加时(秒)</summary>
        public int addTimeSecond;
        /// <summary>通1关加多少金币到金猪</summary>
        public int pigAddGold;
        /// <summary>存到多少可领取</summary>
        public int pigGetGold;
        /// <summary>金猪存储上限</summary>
        public int pigMaxGold;
        /// <summary>免费体力每次加多少个</summary>
        public int freeHeartCount;
        /// <summary>最大连击数量</summary>
        public int maxComboCount;
        /// <summary>每个道具使用上限(10-2表示第10关(包含)内上限都是2个)</summary>
        public string itemCanUseMaxStr;
        /// <summary>胜利结算翻倍倍率条(7个)</summary>
        public float[] goldMults;
        /// <summary>是否开启GM功能</summary>
        public bool openGm;
        /// <summary>
        ///     机器人自动操作
        /// </summary>
        public bool isAutoClickEnabled;
        /// <summary>免费重新挑战次数</summary>
        public int freeRestartCount;
        /// <summary>心的上限</summary>
        public int maxHearts;
        /// <summary>心恢复冷却时间(秒)</summary>
        public int heartCooldownDuration;
        /// <summary>清理槽位复活后加时(秒)</summary>
        public int reviveAddTime;

        public int GetItemCanUseMax(int level) => ConfigSetting.GetItemCanUseMax(level);
    }

    internal override void CacheData(object key, ConfigInfoBase info)
    {
        var infoSetting = info as InfoSetting;
        var id = key.ToString();

        switch (id)
        {
            case "everyLevelIncreaseDifficulty":
                Setting.everyLevelIncreaseDifficulty = infoSetting.paramInt;
                break;
            case "startHuaCount":
                Setting.startHuaCount = infoSetting.paramInt;
                break;
            case "addHuaCount":
                Setting.addHuaCount = infoSetting.paramInt;
                break;
            case "maxHuaCount":
                Setting.maxHuaCount = infoSetting.paramInt;
                break;
            case "startMajiangCount":
                Setting.startMajiangCount = infoSetting.paramInt;
                break;
            case "addMajiangCount":
                Setting.addMajiangCount = infoSetting.paramInt;
                break;
            case "maxMajiangCount":
                Setting.maxMajiangCount = infoSetting.paramInt;
                break;
            case "guideItemCount":
                Setting.guideItemCount = infoSetting.paramInt;
                break;
            case "reliveCount":
                Setting.reliveCount = infoSetting.paramInt;
                break;
            case "clearSlotBarNeedGold":
                Setting.clearSlotBarNeedGold = infoSetting.paramInt;
                break;
            case "addTimeNeedGold":
                Setting.addTimeNeedGold = infoSetting.paramInt;
                break;
            case "addTimeSecond":
                Setting.addTimeSecond = infoSetting.paramInt;
                break;
            case "pigAddGold":
                Setting.pigAddGold = infoSetting.paramInt;
                break;
            case "pigGetGold":
                Setting.pigGetGold = infoSetting.paramInt;
                break;
            case "pigMaxGold":
                Setting.pigMaxGold = infoSetting.paramInt;
                break;
            case "freeHeartCount":
                Setting.freeHeartCount = infoSetting.paramInt;
                break;
            case "maxComboCount":
                Setting.maxComboCount = infoSetting.paramInt;
                break;
            case "itemCanUseMax":
                Setting.itemCanUseMaxStr = infoSetting.paramString;
                break;
            case "goldMult":
                var goldMult = infoSetting.paramString;
                var mults = goldMult.Split(',');
                Setting.goldMults = new float[mults.Length];
                for (int i = 0; i < mults.Length; i++)
                {
                    float.TryParse(mults[i], out Setting.goldMults[i]);
                }
                break;
            case "openGm":
                Setting.openGm = infoSetting.paramInt == 1;
                break;
            case "isAutoClickEnabled":
                Setting.isAutoClickEnabled = infoSetting.paramInt == 1;
                break;
            case "freeRestartCount":
                Setting.freeRestartCount = infoSetting.paramInt;
                break;
            case "maxHearts":
                Setting.maxHearts = infoSetting.paramInt;
                break;
            case "heartCooldownDuration":
                Setting.heartCooldownDuration = infoSetting.paramInt;
                break;
            case "reviveAddTime":
                Setting.reviveAddTime = infoSetting.paramInt;
                break;
        }
    }



    private static Dictionary<int, int> _canUseItemDic;
    public static int GetItemCanUseMax(int level)
    {
        if (_canUseItemDic == null)
        {
            _canUseItemDic = new Dictionary<int, int>();
            var itemAry = Setting.itemCanUseMaxStr.Split('|');
            for (int i = 0; i < itemAry.Length; i++)
            {
                var ary = itemAry[i].Split('-');
                if (ary.Length == 2 && int.TryParse(ary[0], out int key) && int.TryParse(ary[1], out int value))
                {
                    _canUseItemDic[key] = value;
                }
            }
        }

        var canUseItemCount = 4;
        foreach (var item in _canUseItemDic)
        {
            if (level <= item.Key)
            {
                canUseItemCount = item.Value;
                break;
            }
        }
        return canUseItemCount;
    }
}