<?xml version="1.0" encoding="utf-8"?>
<packageDescription id="kzjqd5ah">
  <resources>
    <image id="p6dve" name="lock2.png" path="/images/"/>
    <image id="p6dvf" name="lock.png" path="/images/"/>
    <image id="p6dvm" name="unlockText.png" path="/images/"/>
    <component id="p6dvn" name="BattlePanel.xml" path="/" exported="true"/>
    <component id="jspm19" name="SettingPanel.xml" path="/" exported="true"/>
    <image id="jspm1a" name="btnHome2.png" path="/images/"/>
    <image id="jspm1g" name="titleSetting.png" path="/images/"/>
    <image id="jspm1i" name="btnRestart.png" path="/images/"/>
    <image id="jspm1k" name="model.png" path="/"/>
    <component id="hnff1o" name="GuideComp.xml" path="/" exported="true"/>
    <component id="qoqy1p" name="TapMsg.xml" path="/"/>
    <image id="qoqy1q" name="tapMsg.png" path="/images/" scale="9grid" scale9grid="73,26,146,52"/>
    <component id="qoqy1r" name="Hand.xml" path="/"/>
    <component id="qoqy1s" name="GuideMask.xml" path="/"/>
    <component id="wupj1v" name="PrgResult.xml" path="/"/>
    <image id="qjed24" name="sunLight.png" path="/images/"/>
    <image id="fs2p27" name="hand1.png" path="/images/"/>
    <image id="fs2p28" name="hand2.png" path="/images/"/>
    <component id="fs2p2a" name="MouseHand.xml" path="/" exported="true"/>
    <component id="tst72b" name="adTip.xml" path="/"/>
    <image id="q9n92l" name="taskProgress.png" path="/images/"/>
    <component id="q9n92n" name="FeatureProgress.xml" path="/"/>
    <image id="rckv2r" name="btnCoin.png" path="/images/"/>
    <component id="g23930" name="Coin.xml" path="/" exported="true"/>
    <component id="9mk633" name="btnCreate.xml" path="/"/>
    <component id="k64y35" name="btnItem.xml" path="/" exported="true"/>
    <component id="mr9u36" name="LeftTimeBar.xml" path="/"/>
    <component id="mr9u37" name="StarBar.xml" path="/"/>
    <image id="mr9u38" name="star.png" path="/images/"/>
    <component id="lia539" name="ComboBar.xml" path="/"/>
    <image id="oak13a" name="coinBg.png" path="/images/" scale="9grid" scale9grid="50,12,100,24"/>
    <component id="oak13b" name="ResultFailPanel.xml" path="/" exported="true"/>
    <component id="oak13e" name="btnContinueByVideo.xml" path="/"/>
    <component id="oak13g" name="ResultWinPanel.xml" path="/" exported="true"/>
    <image id="g14v3j" name="itemBulb.png" path="/images/"/>
    <image id="g14v3k" name="itemShuffle.png" path="/images/"/>
    <image id="g14v3l" name="itemMagnet.png" path="/images/"/>
    <image id="g14v3m" name="itemTurn.png" path="/images/"/>
    <image id="cqln3o" name="multArrow.png" path="/images/"/>
    <image id="cqln3p" name="multBar.png" path="/images/"/>
    <component id="cqln3q" name="multBar.xml" path="/"/>
    <component id="cqln16" name="btnShare.xml" path="/" exported="true"/>
    <component id="cqln3t" name="btnMultReward.xml" path="/" exported="true"/>
    <image id="cqln3u" name="coin.png" path="/images/"/>
    <component id="cqln3v" name="ExitPanel.xml" path="/" exported="true"/>
    <component id="cqln3w" name="btnExit.xml" path="/" exported="true"/>
    <image id="cqln3x" name="exit.png" path="/images/"/>
    <component id="givr3y" name="BuyItemPanel.xml" path="/" exported="true"/>
    <component id="j9tu3z" name="RewardPanel.xml" path="/" exported="true"/>
    <component id="j9tu40" name="RewardItem.xml" path="/"/>
    <image id="ss2k41" name="btnPause.png" path="/images/"/>
    <image id="ss2k42" name="imgBottom.png" path="/images/" scale="9grid" scale9grid="1,0,2,189" atlas="1" duplicatePadding="true"/>
    <image id="ss2k43" name="imgTop.png" path="/images/" scale="9grid" scale9grid="2,0,4,82" atlas="1" duplicatePadding="true"/>
    <image id="ss2k44" name="levelBg.png" path="/images/" scale="9grid" scale9grid="17,19,6,5"/>
    <image id="ss2k45" name="timeBg.png" path="/images/" scale="9grid" scale9grid="23,23,8,7"/>
    <component id="ss2k46" name="btnPause.xml" path="/"/>
    <image id="cpy347" name="btnAdd.png" path="/images/"/>
    <image id="cpy348" name="itemBg.png" path="/images/" scale="9grid" scale9grid="13,14,4,9" atlas="1"/>
    <image id="cpy349" name="itemNumBg.png" path="/images/" scale="9grid" scale9grid="28,14,2,28"/>
    <image id="cpy34a" name="comboProgressBar.png" path="/images/"/>
    <image id="cpy34b" name="comboProgressBg.png" path="/images/" scale="9grid" scale9grid="10,0,6,20"/>
    <image id="cpy34c" name="btnCircle.png" path="/images/"/>
    <image id="cpy34d" name="soundOff.png" path="/images/"/>
    <image id="cpy34e" name="soundOn.png" path="/images/"/>
    <image id="cpy34f" name="titlePause.png" path="/images/"/>
    <image id="cpy34g" name="vibrateOff.png" path="/images/"/>
    <image id="cpy34h" name="vibrateOn.png" path="/images/"/>
    <component id="cpy34i" name="btnSound.xml" path="/"/>
    <component id="cpy34j" name="btnVibrate.xml" path="/"/>
    <image id="t5i14m" name="comboProgressBar2.png" path="/images/"/>
    <image id="t5i14o" name="btn_buy.png" path="/images/"/>
    <image id="t5i14p" name="btn_free.png" path="/images/"/>
    <image id="t5i14r" name="image_nameBg.png" path="/images/" scale="9grid" scale9grid="32,0,8,58"/>
    <image id="t5i14s" name="image_countBg.png" path="/images/"/>
    <image id="t5i14t" name="image_combBg.png" path="/images/"/>
    <image id="t5i14u" name="image_combText.png" path="/images/"/>
    <image id="t5i14v" name="font_combo_00.png" path="/fonts/"/>
    <image id="t5i14w" name="font_combo_01.png" path="/fonts/"/>
    <image id="t5i14x" name="font_combo_02.png" path="/fonts/"/>
    <image id="t5i14y" name="font_combo_03.png" path="/fonts/"/>
    <image id="t5i14z" name="font_combo_04.png" path="/fonts/"/>
    <image id="t5i150" name="font_combo_05.png" path="/fonts/"/>
    <image id="t5i151" name="font_combo_06.png" path="/fonts/"/>
    <image id="t5i152" name="font_combo_07.png" path="/fonts/"/>
    <image id="t5i153" name="font_combo_08.png" path="/fonts/"/>
    <image id="t5i154" name="font_combo_09.png" path="/fonts/"/>
    <font id="t5i155" name="FontCombo.fnt" path="/" exported="true"/>
    <component id="t5i156" name="Combo.xml" path="/"/>
    <image id="t5i157" name="image_bg.png" path="/images/" scale="9grid" scale9grid="20,19,6,8" atlas="1"/>
    <image id="t5i158" name="btn_remove.png" path="/images/"/>
    <component id="t5i159" name="btnContinueGold.xml" path="/"/>
    <image id="t5i15b" name="btn_buyRemove.png" path="/images/"/>
    <image id="t5i15c" name="coinBig.png" path="/images/"/>
    <image id="t5i15d" name="image_heart.png" path="/images/"/>
    <image id="t5i15e" name="image_line.png" path="/images/" scale="tile"/>
    <image id="t5i15f" name="image_line2.png" path="/images/" scale="9grid" scale9grid="4,0,8,3"/>
    <image id="t5i15h" name="image_bgResult.png" path="/images/" scale="9grid" scale9grid="0,151,705,3"/>
    <image id="t5i15i" name="image_result_coin.png" path="/images/"/>
    <image id="t5i15j" name="image_result_countBg.png" path="/images/" scale="9grid" scale9grid="28,13,52,16"/>
    <image id="t5i15k" name="image_btnIcon_ad.png" path="/images/"/>
    <image id="t5i15l" name="image_btnText_receive.png" path="/images/"/>
    <component id="t5i14g" name="BtnReceive.xml" path="/"/>
    <image id="t5i15m" name="prg_giftIcon.png" path="/images/"/>
    <image id="t5i15n" name="prg_result_bar.png" path="/images/"/>
    <image id="t5i15o" name="prg_result_bg.png" path="/images/" scale="9grid" scale9grid="13,0,2,26"/>
    <component id="ux5k5p" name="btnContinueFree.xml" path="/"/>
    <image id="ux5k5q" name="btn_remove_title1.png" path="/images/" exported="true"/>
    <image id="ux5k5r" name="btn_buyRemove_title1.png" path="/images/" exported="true"/>
    <image id="s9fh5s" name="btn_buyRemove_title2.png" path="/images/" exported="true"/>
    <image id="s9fh5t" name="btn_remove_title2.png" path="/images/" exported="true"/>
    <image id="s9fh5u" name="image_result_arrow.png" path="/images/"/>
    <image id="s9fh5v" name="image_result_star.png" path="/images/"/>
    <image id="s9fh5w" name="image_result_desc.png" path="/images/"/>
    <image id="iz8v5x" name="image_tips_arrow.png" path="/images/"/>
    <image id="iz8v5y" name="image_tips_bg.png" path="/images/" scale="9grid" scale9grid="36,31,5,13"/>
    <component id="iz8v60" name="ItemTips.xml" path="/"/>
    <image id="dvx261" name="prg_sound_bar.png" path="/images/" scale="9grid" scale9grid="18,0,8,37"/>
    <image id="dvx262" name="prg_sound_grip.png" path="/images/"/>
    <image id="dvx263" name="prg_sound_bg.png" path="/images/" scale="9grid" scale9grid="22,0,10,45"/>
    <image id="dvx266" name="prg_sound_bar2.png" path="/images/" scale="9grid" scale9grid="18,0,7,37"/>
    <image id="dvx267" name="123.png" path="/images/"/>
    <component id="dvx269" name="SliderSound_grip.xml" path="/"/>
    <component id="dvx26a" name="SliderSound.xml" path="/"/>
    <image id="dvx26b" name="image_icon_bgm.png" path="/images/"/>
    <image id="dvx26c" name="image_icon_vibration.png" path="/images/"/>
    <image id="dvx26d" name="image_icon_sfx.png" path="/images/"/>
    <image id="dvx26e" name="font_01.png" path="/images/"/>
    <image id="dvx26f" name="font_02.png" path="/images/"/>
    <image id="dvx26g" name="font_03.png" path="/images/"/>
    <image id="dvx26h" name="font_04.png" path="/images/"/>
    <image id="jr8k6j" name="image_titleBg.png" path="/images/"/>
    <image id="jr8k6k" name="image_titleText_pause.png" path="/images/"/>
    <image id="jr8k6l" name="image_titleText_set.png" path="/images/"/>
    <image id="jr8k6m" name="prg_sound_bg2.png" path="/images/" scale="9grid" scale9grid="22,0,10,45"/>
    <image id="s74r6r" name="guideTextBg.png" path="/images/" scale="9grid" scale9grid="35,0,9,74"/>
    <image id="ne186s" name="btn_text_free.png" path="/images/"/>
    <image id="kery6t" name="image_itemBg.png" path="/images/"/>
    <image id="trpz6v" name="image_nameBg2.png" path="/images/" scale="9grid" scale9grid="18,7,5,16"/>
    <component id="bymn6y" name="BtnRestart.xml" path="/"/>
    <image id="whrg6z" name="itemlock.png" path="/images/"/>
    <image id="mo9771" name="itemUseFlag.png" path="/images/" scale="9grid" scale9grid="7,2,7,3"/>
    <image id="mo9772" name="itemUseFlag1.png" path="/images/" scale="9grid" scale9grid="5,1,6,2"/>
    <image id="dh3g73" name="btn_restart.png" path="/images/"/>
    <component id="ul9w74" name="CollectPanel.xml" path="/" exported="true"/>
    <image id="ul9w75" name="image_titleText_newmj.png" path="/images/"/>
    <image id="ul9w76" name="btnNext.png" path="/images/"/>
    <component id="ul9w77" name="ItemMajiang.xml" path="/"/>
    <image id="s4ex78" name="btnIcon_restart.png" path="/images/"/>
    <component id="s4ex79" name="GetHeartPanel.xml" path="/" exported="true"/>
    <component id="s4ex7a" name="btnGetHeart.xml" path="/"/>
    <component id="s4ex7b" name="ResultContinue.xml" path="/"/>
    <component id="s4ex7c" name="btnCom.xml" path="/"/>
    <image id="s4ex7d" name="bg_blue.png" path="/images/" scale="9grid" scale9grid="52,0,6,114"/>
    <image id="s4ex7e" name="btn_text_not.png" path="/images/"/>
    <image id="s4ex7f" name="btn_text_hall.png" path="/images/"/>
    <image id="s4ex7g" name="bg_red.png" path="/images/" scale="9grid" scale9grid="55,0,8,114"/>
    <image id="s4ex7h" name="btn_text_restart.png" path="/images/"/>
    <image id="o6rc7i" name="124.png" path="/images/"/>
    <image id="o6rc7k" name="image_passTipsBg.png" path="/images/" scale="9grid" scale9grid="174,32,7,15"/>
    <image id="o6rc7l" name="125.png" path="/images/"/>
    <image id="bvh47m" name="bg_green.png" path="/images/" scale="9grid" scale9grid="55,0,6,125"/>
    <image id="bvh47n" name="bg_yellow.png" path="/images/" scale="9grid" scale9grid="56,0,12,125"/>
    <component id="bvh47o" name="btnComAd.xml" path="/"/>
    <image id="bvh47p" name="btn_text_doub.png" path="/images/"/>
    <image id="mxgl7q" name="126.png" path="/images/"/>
    <image id="mxgl7r" name="btn_text_free2.png" path="/images/"/>
    <image id="mxgl7s" name="image_line3.png" path="/images/" scale="9grid" scale9grid="0,2,3,2"/>
    <image id="mxgl7t" name="image_btnText_receive2.png" path="/images/"/>
    <image id="e3xr7u" name="btn_text_addTime.png" path="/images/"/>
    <image id="e3xr7v" name="btn_text_resurrect.png" path="/images/"/>
    <image id="e3xr7w" name="btn_text_exit.png" path="/images/"/>
    <image id="e3xr7x" name="btn_text_restart2.png" path="/images/"/>
    <image id="e3xr7y" name="btn_text_three.png" path="/images/"/>
    <component id="i26m7z" name="BtnTestMenu.xml" path="/"/>
    <image id="i26m80" name="btn_com.png" path="/images/" scale="9grid" scale9grid="36,35,17,17"/>
    <component id="i26m81" name="TestPanel.xml" path="/" exported="true"/>
    <component id="i26m82" name="BtnTest.xml" path="/"/>
    <misc id="hl2b83" name="HuoDeXJS.atlas" path="/spine/"/>
    <image id="hl2b84" name="HuoDeXJS.png" path="/spine/"/>
    <misc id="hl2b85" name="zhuyezi.atlas" path="/spine/"/>
    <image id="hl2b86" name="zhuyezi.png" path="/spine/"/>
    <spine id="hl2b87" name="HuoDeXJS.skel" path="/spine/" width="600" height="300" require="hl2b83,hl2b84" atlasNames="HuoDeXJS" anchor="300,130" shader="FairyGUI/Image"/>
    <spine id="hl2b88" name="zhuyezi.skel" path="/spine/" width="200" height="200" require="hl2b85,hl2b86" atlasNames="zhuyezi" anchor="103,100" shader="FairyGUI/Image"/>
    <component id="hl2b89" name="EffectEiminate.xml" path="/" exported="true"/>
    <component id="xzg38a" name="CompWin.xml" path="/"/>
    <image id="nem38b" name="btn_text_restart3.png" path="/images/"/>
  </resources>
  <publish name=""/>
</packageDescription>