<?xml version="1.0" encoding="utf-8"?>
<component size="720,1280">
  <controller name="c1" pages="0,,1," selected="0">
    <remark page="0" value="设置"/>
    <remark page="1" value="暂停"/>
  </controller>
  <controller name="c2" pages="0,,1," selected="0">
    <remark page="0" value="免费重新挑战"/>
    <remark page="1" value="扣体力挑战"/>
  </controller>
  <displayList>
    <image id="n0_jspm" name="n0" src="jspmq" fileName="images/popBg.png" pkg="dzm3l2gb" xy="40,386" size="640,519" group="n17_t5i1">
      <relation target="n20_10avx" sidePair="bottomext-bottom"/>
    </image>
    <image id="n11_cpy3" name="n11" src="jr8k6j" fileName="images/image_titleBg.png" xy="174,375" group="n17_t5i1"/>
    <image id="n37_jr8k" name="n37" src="jr8k6l" fileName="images/image_titleText_set.png" xy="308,392" group="n17_t5i1">
      <gearDisplay controller="c1" pages="0"/>
    </image>
    <image id="n36_jr8k" name="n36" src="jr8k6k" fileName="images/image_titleText_pause.png" xy="308,392" group="n17_t5i1">
      <gearDisplay controller="c1" pages="1"/>
    </image>
    <component id="n10_jspm" name="btnClose" src="p6dvo" fileName="IconButton.xml" pkg="dzm3l2gb" xy="525,384" size="91,92" group="n17_t5i1">
      <Button icon="ui://dzm3l2gbjspmp"/>
    </component>
    <image id="n21_dvx2" name="n21" src="dvx26b" fileName="images/image_icon_bgm.png" xy="149,530" group="n19_10avx"/>
    <image id="n22_dvx2" name="n22" src="dvx26d" fileName="images/image_icon_sfx.png" xy="150,631" group="n19_10avx"/>
    <image id="n23_dvx2" name="n23" src="dvx26c" fileName="images/image_icon_vibration.png" xy="149,736" group="n19_10avx"/>
    <image id="n24_dvx2" name="n24" src="dvx26e" fileName="images/font_01.png" xy="531,751" group="n19_10avx"/>
    <image id="n25_dvx2" name="n25" src="dvx26f" fileName="images/font_02.png" xy="232,751" group="n19_10avx"/>
    <image id="n26_dvx2" name="n26" src="dvx26g" fileName="images/font_03.png" xy="531,542" group="n19_10avx"/>
    <image id="n27_dvx2" name="n27" src="dvx26h" fileName="images/font_04.png" xy="232,542" group="n19_10avx"/>
    <image id="n29_dvx2" name="n29" src="dvx26g" fileName="images/font_03.png" xy="531,646" group="n19_10avx"/>
    <image id="n30_dvx2" name="n30" src="dvx26h" fileName="images/font_04.png" xy="232,646" group="n19_10avx"/>
    <component id="n31_dvx2" name="sliderBgm" src="dvx26a" fileName="SliderSound.xml" xy="284,537" group="n19_10avx">
      <Slider value="50" max="100"/>
    </component>
    <component id="n32_dvx2" name="sliderSfx" src="dvx26a" fileName="SliderSound.xml" xy="284,641" group="n19_10avx">
      <Slider value="50" max="100"/>
    </component>
    <component id="n16_cpy3" name="btnVibration" src="cpy34j" fileName="btnVibrate.xml" xy="284,746" group="n19_10avx">
      <Button checked="true"/>
    </component>
    <group id="n19_10avx" name="n19" xy="149,530" size="412,271" group="n20_10avx" advanced="true"/>
    <image id="n12_cpy3" name="n12" src="t5i15f" fileName="images/image_line2.png" xy="167,857" size="386,3" group="n18_10avx"/>
    <component id="n9_jspm" name="btnRestart" src="s4ex7c" fileName="btnCom.xml" xy="174,894" size="371,114" group="n18_10avx">
      <gearDisplay controller="c2" pages="0"/>
      <Button icon="ui://kzjqd5ahs4ex7h"/>
    </component>
    <component id="n38_nem3" name="btnRestartHp" src="s4ex7c" fileName="btnCom.xml" xy="174,894" size="371,114" group="n18_10avx">
      <gearDisplay controller="c2" pages="1"/>
      <Button icon="ui://kzjqd5ahnem38b"/>
    </component>
    <component id="n8_jspm" name="btnHome" src="s4ex7c" fileName="btnCom.xml" xy="174,1018" size="371,114" group="n18_10avx">
      <Button icon="ui://kzjqd5ahe3xr7w"/>
      <property target="bg" propertyId="1" value="ui://kzjqd5ahs4ex7g"/>
    </component>
    <group id="n18_10avx" name="boxPause" xy="167,857" size="386,275" group="n20_10avx" advanced="true">
      <gearDisplay controller="c1" pages="1"/>
    </group>
    <group id="n20_10avx" name="n20" xy="149,530" size="412,271" group="n17_t5i1" advanced="true" layout="vt" lineGap="56" excludeInvisibles="true"/>
    <group id="n17_t5i1" name="boxContent" xy="40,375" size="640,530" advanced="true">
      <relation target="" sidePair="center-center,middle-middle"/>
    </group>
  </displayList>
</component>