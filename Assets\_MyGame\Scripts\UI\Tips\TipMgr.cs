using System;
using FairyGUI;
using UnityEngine;

public class TipMgr
{
    public static void ShowTip(string content, float duration = 1.5f)
    {
        MsgTip.Create(content, duration);
    }

    public static void ShowRewards(ItemVo[] items, GObject flyTarget = null, Action OnClosed = null, float autoHideTime = 0)
    {
        Vector2? flyTargetPos = null;
        if (flyTarget != null && !flyTarget.isDisposed)
        {
            var pos = flyTarget.LocalToRoot(Vector2.zero, GRoot.inst);
            // 计算目标的中心位置
            if (!flyTarget.pivotAsAnchor)
            {
                pos.x += flyTarget.width * 0.5f;
                pos.y += flyTarget.height * 0.5f;
            }

            flyTargetPos = pos;
        }
        Panel.Create((GetRewardPanel panel) =>
        {
            panel.OnClosed = (type) =>
            {
                OnClosed?.Invoke();
            };
            panel.SetData(items, flyTargetPos, autoHideTime);
        });
    }
}