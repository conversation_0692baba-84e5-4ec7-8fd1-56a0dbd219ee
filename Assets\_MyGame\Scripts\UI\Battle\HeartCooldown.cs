using System;
using UnityEngine;

public class HeartCooldown : MonoBehaviour
{
    public static HeartCooldown Instance;

    [NonSerialized] public int MaxHearts; // 心的上限
    [NonSerialized] public float CooldownDuration; // 恢复周期，以秒为单位
    private DateTime? nextCooldownTime; // 下一次心恢复的时间
    private int _currentHearts = -1;

    private void Awake()
    {
        Instance = this;
    }

    public void Init(int curHearts, int cooldownSecondTicks)
    {
        _currentHearts = curHearts;
        if (cooldownSecondTicks != 0 && _currentHearts < MaxHearts)
        {
            var offsetTime = cooldownSecondTicks - GetDateTimeTotalSecond(Now);
            if (offsetTime < 0)
            {
                var time = -offsetTime / CooldownDuration + 1;
                var addHeartCount = Mathf.FloorToInt((float)time);
                var leftTime = (1 - (time - addHeartCount)) * CooldownDuration;

                Debug.Log("=====time:" + time + "   add:" + addHeartCount + "  leftTime:" + leftTime);
                RecoverHeart(addHeartCount, leftTime);
            }
            else
            {
                nextCooldownTime = Now.AddSeconds(offsetTime);
            }
        }
    }

    private void Update()
    {
        if (CurrentHearts < MaxHearts && Now >= nextCooldownTime)
        {
            RecoverHeart(1, CooldownDuration);
        }
    }


    public void RecoverHeart(int amount, float leftTime = 0)
    {
        if (!IsFull)
        {
            CurrentHearts += amount;
            CurrentHearts = Mathf.Min(CurrentHearts, MaxHearts);
            if (CurrentHearts < MaxHearts)
            {
                if (leftTime != 0)
                {
                    nextCooldownTime = Now.AddSeconds(leftTime);
                    StorageMgr.HeartCooldownSecondTicks = GetDateTimeTotalSecond((DateTime)nextCooldownTime);
                }
            }
            else
            {
                nextCooldownTime = null;
            }
        }
    }

    public void ConsumeHeart(int amount = 1)
    {
        CurrentHearts -= amount;
        if (CurrentHearts < 0) CurrentHearts = 0;
        // 如果心数从满变为不满，可以立即开始恢复倒计时
        if (CurrentHearts < MaxHearts && nextCooldownTime == null)
        {
            nextCooldownTime = Now.AddSeconds(CooldownDuration);
            StorageMgr.HeartCooldownSecondTicks = GetDateTimeTotalSecond((DateTime)nextCooldownTime);
        }
    }

    private int GetDateTimeTotalSecond(DateTime time)
    {
        return (int)((time.ToUniversalTime().Ticks - 621355968000000000) / 10000000);
    }

    // 获取当前心数
    public int GetCurrentHearts()
    {
        return CurrentHearts;
    }

    // 获取剩余cd时间
    public int GetRemainingCooldownTime()
    {
        if (nextCooldownTime == null)
            return 0;
        TimeSpan remainingTime = (DateTime)nextCooldownTime - Now;
        return Mathf.FloorToInt((float)remainingTime.TotalSeconds);
    }

    private DateTime Now
    {
        get
        {
            return DateTime.UtcNow;
        }
    }

    public bool IsFull
    {
        get { return CurrentHearts >= MaxHearts; }
    }

    private int CurrentHearts
    {
        get
        {
            return _currentHearts;
        }
        set
        {
            _currentHearts = value;
            if (_currentHearts > MaxHearts)
                _currentHearts = MaxHearts;

            StorageMgr.SetHeart(_currentHearts);
        }
    }
}